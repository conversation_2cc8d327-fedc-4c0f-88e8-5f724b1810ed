#!/usr/bin/env python
"""
Test script to simulate venue creation wizard flow
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CozyWish.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from accounts_app.models import ServiceProviderProfile

User = get_user_model()

def test_venue_wizard():
    """Test the venue creation wizard flow"""
    client = Client()
    
    # Get or create a test service provider user
    try:
        user = User.objects.get(email='<EMAIL>')
    except User.DoesNotExist:
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Provider',
            user_type='service_provider'
        )
        
        # Create service provider profile
        ServiceProviderProfile.objects.create(
            user=user,
            business_name='Test Spa',
            business_type='spa',
            phone='************',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
    
    # Login
    client.login(email='<EMAIL>', password='testpass123')
    
    print("Testing venue creation wizard...")
    
    # Step 1: Basic information
    print("\n1. Testing basic step...")
    response = client.get('/venues/provider/create/wizard/basic/')
    print(f"Basic step GET status: {response.status_code}")
    
    # Submit basic information
    basic_data = {
        'venue_name': 'Test Wellness Center',
        'short_description': 'A beautiful wellness center offering relaxation and rejuvenation services.',
    }
    
    response = client.post('/venues/provider/create/wizard/basic/', basic_data)
    print(f"Basic step POST status: {response.status_code}")
    if response.status_code == 302:
        print(f"Redirected to: {response.url}")
    else:
        print("Form errors:", getattr(response.context.get('form'), 'errors', 'No form in context'))
    
    # Step 2: Location information
    print("\n2. Testing location step...")
    response = client.get('/venues/provider/create/wizard/location/')
    print(f"Location step GET status: {response.status_code}")
    
    # Submit location information
    location_data = {
        'state': 'CA',
        'county': 'Los Angeles',
        'city': 'Los Angeles',
        'street_number': '123',
        'street_name': 'Main Street',
    }
    
    response = client.post('/venues/provider/create/wizard/location/', location_data)
    print(f"Location step POST status: {response.status_code}")
    if response.status_code == 302:
        print(f"Redirected to: {response.url}")
    else:
        print("Form errors:", getattr(response.context.get('form'), 'errors', 'No form in context'))
        if hasattr(response.context.get('form'), 'non_field_errors'):
            print("Non-field errors:", response.context['form'].non_field_errors())

if __name__ == '__main__':
    test_venue_wizard()
